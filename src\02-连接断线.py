import win32com.client
import time
import os
import math
import pythoncom
# from collections import defaultdict  # 不再需要


def get_polyline_endpoints(polyline):
    """获取多段线的起点和终点坐标"""
    try:
        # 获取所有坐标
        coords_variant = polyline.Coordinates

        # 将VARIANT转换为Python列表
        coords = list(coords_variant)

        # 坐标是以 [x1, y1, x2, y2, ...] 格式存储的
        if len(coords) < 4:  # 至少需要2个点（4个坐标值）
            return None, None

        # 获取起点
        start_point = (coords[0], coords[1])

        # 获取终点（最后一个点）
        end_point = (coords[-2], coords[-1])

        return start_point, end_point

    except Exception as e:
        print(f"获取端点坐标时出错: {str(e)}")
        try:
            # 备用方法：使用GetBoundingBox
            min_point = win32com.client.VARIANT(pythoncom.VT_ARRAY | pythoncom.VT_R8, [0.0, 0.0, 0.0])
            max_point = win32com.client.VARIANT(pythoncom.VT_ARRAY | pythoncom.VT_R8, [0.0, 0.0, 0.0])
            polyline.GetBoundingBox(min_point, max_point)

            # 这只是一个粗略的估计，不是精确的端点
            return None, None
        except:
            return None, None


def get_all_vertices(polyline):
    """获取多段线的所有顶点坐标"""
    vertices = []
    try:
        # 获取所有坐标
        coords_variant = polyline.Coordinates
        coords = list(coords_variant)

        # 将坐标列表转换为点列表
        # 坐标是以 [x1, y1, x2, y2, ...] 格式存储的
        for i in range(0, len(coords), 2):
            if i + 1 < len(coords):
                vertices.append((coords[i], coords[i + 1]))

        return vertices

    except Exception as e:
        print(f"获取顶点坐标时出错: {str(e)}")
        return []


def points_are_close(p1, p2, tolerance=0.001):
    """判断两个点是否足够接近"""
    if p1 is None or p2 is None:
        return False

    distance = math.sqrt((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2)
    return distance < tolerance


def find_connected_polylines(polylines, tolerance=0.001):
    """找出相邻的多段线组，用于PE命令合并"""
    groups = []
    used = set()

    for i, poly1 in enumerate(polylines):
        if i in used:
            continue

        # 获取当前多段线的端点
        start1, end1 = get_polyline_endpoints(poly1)
        if not start1 or not end1:
            continue

        # 当前组，从这个多段线开始
        current_group = [i]
        used.add(i)

        # 查找与当前组中任何多段线相邻的其他多段线
        found_new = True
        while found_new:
            found_new = False
            for group_idx in current_group[:]:  # 复制列表避免修改时出错
                group_poly = polylines[group_idx]
                group_start, group_end = get_polyline_endpoints(group_poly)

                for j, poly2 in enumerate(polylines):
                    if j in used:
                        continue

                    start2, end2 = get_polyline_endpoints(poly2)
                    if not start2 or not end2:
                        continue

                    # 检查是否相邻（任意端点接近）
                    if (points_are_close(group_start, start2, tolerance) or
                        points_are_close(group_start, end2, tolerance) or
                        points_are_close(group_end, start2, tolerance) or
                        points_are_close(group_end, end2, tolerance)):

                        current_group.append(j)
                        used.add(j)
                        found_new = True
                        break

        # 只有多于1个多段线的组才需要合并
        if len(current_group) > 1:
            groups.append([polylines[idx] for idx in current_group])

    return groups


def merge_polylines_with_pedit(doc, polyline_group):
    """使用AutoCAD的PEDIT命令合并多段线组"""
    if len(polyline_group) < 2:
        return False

    try:
        # 清除当前选择
        doc.SendCommand("_SELECTIONCLEAR\n")

        # 选择第一个多段线
        first_poly = polyline_group[0]

        # 创建选择集包含第一个多段线
        ss_name = f"PEDIT_{int(time.time() * 1000000)}"
        try:
            doc.SelectionSets.Item(ss_name).Delete()
        except:
            pass

        sset = doc.SelectionSets.Add(ss_name)
        sset.AddItems(win32com.client.VARIANT(pythoncom.VT_ARRAY | pythoncom.VT_DISPATCH, [first_poly]))
        sset.Select(5)  # 选择选择集中的对象

        # 开始PEDIT命令
        doc.SendCommand("_PEDIT\n")  # 启动PEDIT命令
        doc.SendCommand("\n")        # 选择当前选择的对象
        doc.SendCommand("J\n")       # Join选项

        # 选择其他要合并的多段线
        for poly in polyline_group[1:]:
            # 创建临时选择集
            temp_ss_name = f"TEMP_{int(time.time() * 1000000)}"
            try:
                doc.SelectionSets.Item(temp_ss_name).Delete()
            except:
                pass

            temp_sset = doc.SelectionSets.Add(temp_ss_name)
            temp_sset.AddItems(win32com.client.VARIANT(pythoncom.VT_ARRAY | pythoncom.VT_DISPATCH, [poly]))
            temp_sset.Select(5)

            # 清理临时选择集
            try:
                temp_sset.Delete()
            except:
                pass

        doc.SendCommand("\n")        # 完成选择
        doc.SendCommand("\n")        # 退出PEDIT命令

        # 清理选择集
        try:
            sset.Delete()
        except:
            pass

        return True

    except Exception as e:
        print(f"PEDIT命令执行出错: {str(e)}")
        return False


def merge_polylines_in_layer(dwg_file_path, layer_name="450301", tolerance=0.001):
    """合并指定图层中被打断的多段线"""

    print(f"=" * 80)
    print(f"合并图层 '{layer_name}' 中的多段线")
    print(f"=" * 80)
    print(f"DWG文件: {dwg_file_path}")
    print(f"容差: {tolerance}")
    print(f"=" * 80)

    # 检查文件是否存在
    if not os.path.exists(dwg_file_path):
        print(f"错误：文件不存在 - {dwg_file_path}")
        return

    acad = None
    doc = None

    try:
        # 连接AutoCAD
        print("\n正在连接AutoCAD...")
        acad = win32com.client.Dispatch("AutoCAD.Application")
        acad.Visible = True
        time.sleep(1)
        print("✓ 连接成功")

        # 打开文件
        print("\n正在打开文件...")
        doc = acad.Documents.Open(dwg_file_path)
        time.sleep(2)
        doc = acad.ActiveDocument
        print(f"✓ 文件打开成功: {doc.Name}")

        # 创建选择集获取图层中的所有多段线
        ss_name = f"Merge_{layer_name}_{int(time.time() * 1000)}"

        # 删除同名选择集
        try:
            doc.SelectionSets.Item(ss_name).Delete()
        except:
            pass

        sset = doc.SelectionSets.Add(ss_name)

        # 创建过滤器 - 选择指定图层的多段线
        filter_type = win32com.client.VARIANT(
            pythoncom.VT_ARRAY | pythoncom.VT_I2,
            [0, 8]  # 0=对象类型, 8=图层
        )
        filter_data = win32com.client.VARIANT(
            pythoncom.VT_ARRAY | pythoncom.VT_VARIANT,
            ["LWPOLYLINE", layer_name]
        )

        # 选择对象
        sset.Select(5, None, None, filter_type, filter_data)  # 5 = acSelectionSetAll

        print(f"\n找到 {sset.Count} 个多段线对象")

        # 收集所有多段线
        polylines = []
        for i in range(sset.Count):
            polylines.append(sset.Item(i))

        # 查找相邻的多段线组
        print("\n分析多段线连通性...")
        connected_groups = find_connected_polylines(polylines, tolerance)

        print(f"\n发现 {len(connected_groups)} 组相邻的多段线")
        for i, group in enumerate(connected_groups):
            print(f"  第 {i + 1} 组: {len(group)} 个多段线")

        # 使用PEDIT命令合并多段线
        print("\n开始使用PEDIT命令合并多段线...")
        merged_count = 0

        for i, group in enumerate(connected_groups):
            print(f"\n处理第 {i + 1} 组...")

            if len(group) == 1:
                # 只有一个多段线，不需要合并
                print(f"  该组只有1个多段线，跳过")
                continue

            # 使用PEDIT命令合并
            success = merge_polylines_with_pedit(doc, group)

            if success:
                merged_count += 1
                print(f"  ✓ 成功使用PEDIT合并了 {len(group)} 个多段线")
            else:
                print(f"  ✗ PEDIT合并失败")

        # 刷新显示
        doc.Regen(1)  # acActiveViewport

        # 统计结果
        print(f"\n" + "=" * 80)
        print(f"合并完成！")
        print(f"原始多段线数: {len(polylines)}")
        print(f"成功合并的组数: {merged_count}")
        print(f"保持不变的多段线数: {sum(1 for g in connected_groups if len(g) == 1)}")
        print(f"=" * 80)

        # 删除选择集
        try:
            sset.Delete()
        except:
            pass

        # 保存文件（可选）
        save_choice = input("\n是否保存更改？(y/n): ")
        if save_choice.lower() == 'y':
            doc.Save()
            print("✓ 文件已保存")
        else:
            print("✗ 未保存更改")

    except Exception as e:
        print(f"\n✗ 出错: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理COM对象引用
        doc = None
        acad = None


# 主程序
if __name__ == "__main__":
    # 文件路径
    dwg_file_path = r"D:\myWork\2025\0805_桥路处理\data\铁三院0805.dwg"

    # 要处理的图层名称
    layer_name = "450301"

    # 容差值（判断两点是否重合的距离阈值）
    tolerance = 0.1

    # 执行合并操作
    merge_polylines_in_layer(dwg_file_path, layer_name, tolerance)

    input("\n按Enter键退出...")