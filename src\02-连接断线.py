import win32com.client
import time
import os
import math
import pythoncom


def connect_autocad_and_open_file(dwg_file_path):
    """连接AutoCAD并打开DWG文件"""
    print(f"正在连接AutoCAD...")

    try:
        # 连接AutoCAD
        acad = win32com.client.Dispatch("AutoCAD.Application")
        acad.Visible = True
        time.sleep(1)
        print("✓ AutoCAD连接成功")

        # 打开文件
        print(f"正在打开文件: {dwg_file_path}")
        doc = acad.Documents.Open(dwg_file_path)
        time.sleep(2)
        doc = acad.ActiveDocument
        print(f"✓ 文件打开成功: {doc.Name}")

        return acad, doc

    except Exception as e:
        print(f"✗ 连接AutoCAD或打开文件失败: {str(e)}")
        return None, None


def get_polyline_properties(polyline):
    """获取多段线的属性和顶点"""
    try:
        # 获取所有顶点坐标
        coords_variant = polyline.Coordinates
        coords = list(coords_variant)

        # 将坐标列表转换为点列表 [x1, y1, x2, y2, ...] -> [(x1, y1), (x2, y2), ...]
        vertices = []
        for i in range(0, len(coords), 2):
            if i + 1 < len(coords):
                vertices.append((coords[i], coords[i + 1]))

        # 获取其他属性
        properties = {
            'vertices': vertices,
            'layer': polyline.Layer,
            'color': polyline.color,
            'closed': polyline.Closed,
            'linetype': polyline.Linetype,
            'lineweight': polyline.Lineweight,
        }

        # 尝试获取线宽
        try:
            properties['constant_width'] = polyline.ConstantWidth
        except:
            properties['constant_width'] = 0.0

        return properties

    except Exception as e:
        print(f"获取多段线属性时出错: {str(e)}")
        return None


def get_polyline_endpoints(polyline):
    """获取多段线的起点和终点坐标"""
    try:
        coords_variant = polyline.Coordinates
        coords = list(coords_variant)

        if len(coords) < 4:  # 至少需要2个点
            return None, None

        start_point = (coords[0], coords[1])
        end_point = (coords[-2], coords[-1])

        return start_point, end_point

    except Exception as e:
        print(f"获取端点坐标时出错: {str(e)}")
        return None, None


def points_are_close(p1, p2, tolerance=0.001):
    """判断两个点是否足够接近"""
    if p1 is None or p2 is None:
        return False

    distance = math.sqrt((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2)
    return distance < tolerance


def find_connected_polylines(polylines, tolerance=0.001):
    """找出相邻的多段线组"""
    groups = []
    used = set()

    for i, poly1 in enumerate(polylines):
        if i in used:
            continue

        # 获取当前多段线的端点
        start1, end1 = get_polyline_endpoints(poly1)
        if not start1 or not end1:
            continue

        # 当前组，从这个多段线开始
        current_group = [i]
        used.add(i)

        # 查找与当前组中任何多段线相邻的其他多段线
        found_new = True
        while found_new:
            found_new = False
            for group_idx in current_group[:]:  # 复制列表避免修改时出错
                group_poly = polylines[group_idx]
                group_start, group_end = get_polyline_endpoints(group_poly)

                for j, poly2 in enumerate(polylines):
                    if j in used:
                        continue

                    start2, end2 = get_polyline_endpoints(poly2)
                    if not start2 or not end2:
                        continue

                    # 检查是否相邻（任意端点接近）
                    if (points_are_close(group_start, start2, tolerance) or
                        points_are_close(group_start, end2, tolerance) or
                        points_are_close(group_end, start2, tolerance) or
                        points_are_close(group_end, end2, tolerance)):

                        current_group.append(j)
                        used.add(j)
                        found_new = True
                        break

        # 只有多于1个多段线的组才需要合并
        if len(current_group) > 1:
            groups.append([polylines[idx] for idx in current_group])

    return groups


def build_merged_path(polyline_group, tolerance=0.001):
    """构建合并后的路径顶点序列"""
    if len(polyline_group) < 2:
        # 只有一个多段线，直接返回其顶点
        props = get_polyline_properties(polyline_group[0])
        return props['vertices'] if props else []

    # 获取所有多段线的属性
    polyline_props = []
    for poly in polyline_group:
        props = get_polyline_properties(poly)
        if props:
            polyline_props.append(props)

    if not polyline_props:
        return []

    # 开始构建路径 - 从第一个多段线开始
    merged_vertices = polyline_props[0]['vertices'][:]
    used_polylines = {0}

    # 继续添加相邻的多段线
    while len(used_polylines) < len(polyline_props):
        current_end = merged_vertices[-1]  # 当前路径的终点

        found_next = False
        for i, props in enumerate(polyline_props):
            if i in used_polylines:
                continue

            vertices = props['vertices']
            start_point = vertices[0]
            end_point = vertices[-1]

            # 检查是否可以连接到当前路径的终点
            if points_are_close(current_end, start_point, tolerance):
                # 正向连接，跳过第一个点（重复点）
                merged_vertices.extend(vertices[1:])
                used_polylines.add(i)
                found_next = True
                break
            elif points_are_close(current_end, end_point, tolerance):
                # 反向连接，反转顶点顺序，跳过最后一个点（重复点）
                reversed_vertices = vertices[:-1]
                reversed_vertices.reverse()
                merged_vertices.extend(reversed_vertices)
                used_polylines.add(i)
                found_next = True
                break

        if not found_next:
            # 无法找到下一个连接的多段线，可能是断开的
            print(f"警告：无法完全连接所有多段线，已处理 {len(used_polylines)}/{len(polyline_props)} 个")
            break

    return merged_vertices


def create_merged_polyline(doc, vertices, original_polylines):
    """创建合并后的新多段线"""
    if len(vertices) < 2:
        return None

    try:
        # 获取第一个原始多段线的属性作为模板
        template_props = get_polyline_properties(original_polylines[0])
        if not template_props:
            return None

        # 将顶点列表转换为坐标数组 [(x1, y1), (x2, y2), ...] -> [x1, y1, x2, y2, ...]
        coords = []
        for vertex in vertices:
            coords.extend([vertex[0], vertex[1]])

        # 创建VARIANT数组
        coords_variant = win32com.client.VARIANT(
            pythoncom.VT_ARRAY | pythoncom.VT_R8,
            coords
        )

        # 创建新的轻量多段线
        model_space = doc.ModelSpace
        new_polyline = model_space.AddLightWeightPolyline(coords_variant)

        # 设置属性
        new_polyline.Layer = template_props['layer']
        new_polyline.color = template_props['color']
        new_polyline.Linetype = template_props['linetype']
        new_polyline.Lineweight = template_props['lineweight']

        # 设置线宽（如果有）
        if template_props['constant_width'] > 0:
            try:
                new_polyline.ConstantWidth = template_props['constant_width']
            except:
                pass  # 某些情况下可能无法设置

        print(f"✓ 成功创建合并多段线，顶点数: {len(vertices)}")
        return new_polyline

    except Exception as e:
        print(f"✗ 创建新多段线失败: {str(e)}")
        return None


def delete_original_polylines(polylines_to_delete):
    """删除原始多段线"""
    deleted_count = 0
    for polyline in polylines_to_delete:
        try:
            polyline.Delete()
            deleted_count += 1
        except Exception as e:
            print(f"删除多段线时出错: {str(e)}")

    print(f"✓ 成功删除 {deleted_count} 个原始多段线")
    return deleted_count


def analyze_polyline_details(polylines):
    """分析多段线的详细信息"""
    print("\n" + "=" * 80)
    print("多段线详细分析")
    print("=" * 80)

    for i, polyline in enumerate(polylines):
        props = get_polyline_properties(polyline)
        if not props:
            continue

        start, end = get_polyline_endpoints(polyline)

        print(f"\n多段线 #{i+1}:")
        print(f"  顶点数: {len(props['vertices'])}")
        print(f"  起点: ({start[0]:.3f}, {start[1]:.3f})" if start else "  起点: 无法获取")
        print(f"  终点: ({end[0]:.3f}, {end[1]:.3f})" if end else "  终点: 无法获取")
        print(f"  闭合: {'是' if props['closed'] else '否'}")
        print(f"  图层: {props['layer']}")
        print(f"  颜色: {props['color']}")
        print(f"  线型: {props['linetype']}")
        print(f"  线宽: {props['constant_width']}")

        # 打印所有顶点
        print(f"  顶点坐标:")
        for j, vertex in enumerate(props['vertices']):
            print(f"    点{j+1}: ({vertex[0]:.3f}, {vertex[1]:.3f})")


def find_connected_polylines_detailed(polylines, tolerance=0.001):
    """找出相邻的多段线组（详细版本）"""
    print("\n" + "=" * 80)
    print("连通性分析详细过程")
    print("=" * 80)

    groups = []
    used = set()
    isolated_polylines = []  # 孤立的多段线

    for i, poly1 in enumerate(polylines):
        if i in used:
            continue

        # 获取当前多段线的端点
        start1, end1 = get_polyline_endpoints(poly1)
        if not start1 or not end1:
            print(f"\n多段线 #{i+1}: 无法获取端点，跳过")
            continue

        print(f"\n分析多段线 #{i+1}:")
        print(f"  起点: ({start1[0]:.3f}, {start1[1]:.3f})")
        print(f"  终点: ({end1[0]:.3f}, {end1[1]:.3f})")

        # 当前组，从这个多段线开始
        current_group = [i]
        used.add(i)

        # 查找与当前组中任何多段线相邻的其他多段线
        found_connections = []
        found_new = True
        while found_new:
            found_new = False
            for group_idx in current_group[:]:  # 复制列表避免修改时出错
                group_poly = polylines[group_idx]
                group_start, group_end = get_polyline_endpoints(group_poly)

                for j, poly2 in enumerate(polylines):
                    if j in used:
                        continue

                    start2, end2 = get_polyline_endpoints(poly2)
                    if not start2 or not end2:
                        continue

                    # 检查所有可能的连接
                    connections = []
                    if points_are_close(group_start, start2, tolerance):
                        connections.append(f"#{group_idx+1}起点 -> #{j+1}起点")
                    if points_are_close(group_start, end2, tolerance):
                        connections.append(f"#{group_idx+1}起点 -> #{j+1}终点")
                    if points_are_close(group_end, start2, tolerance):
                        connections.append(f"#{group_idx+1}终点 -> #{j+1}起点")
                    if points_are_close(group_end, end2, tolerance):
                        connections.append(f"#{group_idx+1}终点 -> #{j+1}终点")

                    if connections:
                        print(f"  找到连接: {', '.join(connections)}")
                        current_group.append(j)
                        used.add(j)
                        found_new = True
                        found_connections.extend(connections)
                        break

        # 记录结果
        if len(current_group) > 1:
            groups.append([polylines[idx] for idx in current_group])
            print(f"  -> 形成组: 包含多段线 {[idx+1 for idx in current_group]}")
        else:
            isolated_polylines.append((i, poly1))
            print(f"  -> 孤立多段线: #{i+1}")

    print(f"\n连通性分析结果:")
    print(f"  相邻组数: {len(groups)}")
    print(f"  孤立多段线数: {len(isolated_polylines)}")

    if isolated_polylines:
        print(f"\n孤立多段线详情:")
        for idx, poly in isolated_polylines:
            props = get_polyline_properties(poly)
            start, end = get_polyline_endpoints(poly)
            print(f"  多段线 #{idx+1}:")
            print(f"    顶点数: {len(props['vertices']) if props else '未知'}")
            print(f"    起点: ({start[0]:.3f}, {start[1]:.3f})" if start else "    起点: 无法获取")
            print(f"    终点: ({end[0]:.3f}, {end[1]:.3f})" if end else "    终点: 无法获取")

            # 检查为什么孤立
            print(f"    距离最近的其他多段线:")
            min_distance = float('inf')
            closest_poly = None
            for j, other_poly in enumerate(polylines):
                if j == idx:
                    continue
                other_start, other_end = get_polyline_endpoints(other_poly)
                if not other_start or not other_end:
                    continue

                distances = []
                if start and other_start:
                    distances.append(math.sqrt((start[0] - other_start[0])**2 + (start[1] - other_start[1])**2))
                if start and other_end:
                    distances.append(math.sqrt((start[0] - other_end[0])**2 + (start[1] - other_end[1])**2))
                if end and other_start:
                    distances.append(math.sqrt((end[0] - other_start[0])**2 + (end[1] - other_start[1])**2))
                if end and other_end:
                    distances.append(math.sqrt((end[0] - other_end[0])**2 + (end[1] - other_end[1])**2))

                if distances:
                    min_dist = min(distances)
                    if min_dist < min_distance:
                        min_distance = min_dist
                        closest_poly = j

            if closest_poly is not None:
                print(f"      多段线 #{closest_poly+1}, 距离: {min_distance:.3f} (容差: {tolerance})")
                if min_distance > tolerance:
                    print(f"      -> 距离超过容差，无法连接")

    return groups


def merge_polylines_in_layer(dwg_file_path, layer_name="450301", tolerance=0.1):
    """合并指定图层中被打断的多段线"""

    print(f"=" * 80)
    print(f"合并图层 '{layer_name}' 中的多段线")
    print(f"=" * 80)
    print(f"DWG文件: {dwg_file_path}")
    print(f"容差: {tolerance}")
    print(f"=" * 80)

    # 检查文件是否存在
    if not os.path.exists(dwg_file_path):
        print(f"错误：文件不存在 - {dwg_file_path}")
        return

    # 连接AutoCAD并打开文件
    acad, doc = connect_autocad_and_open_file(dwg_file_path)
    if not acad or not doc:
        return

    try:
        # 创建选择集获取图层中的所有多段线
        ss_name = f"Merge_{layer_name}_{int(time.time() * 1000)}"

        # 删除同名选择集
        try:
            doc.SelectionSets.Item(ss_name).Delete()
        except:
            pass

        sset = doc.SelectionSets.Add(ss_name)

        # 创建过滤器 - 选择指定图层的多段线
        filter_type = win32com.client.VARIANT(
            pythoncom.VT_ARRAY | pythoncom.VT_I2,
            [0, 8]  # 0=对象类型, 8=图层
        )
        filter_data = win32com.client.VARIANT(
            pythoncom.VT_ARRAY | pythoncom.VT_VARIANT,
            ["LWPOLYLINE", layer_name]
        )

        # 选择对象
        sset.Select(5, None, None, filter_type, filter_data)  # 5 = acSelectionSetAll

        print(f"\n找到 {sset.Count} 个多段线对象")

        # 收集所有多段线
        polylines = []
        for i in range(sset.Count):
            polylines.append(sset.Item(i))

        # 详细分析所有多段线
        analyze_polyline_details(polylines)

        # 查找相邻的多段线组（详细版本）
        connected_groups = find_connected_polylines_detailed(polylines, tolerance)

        print(f"\n" + "=" * 80)
        print(f"发现 {len(connected_groups)} 组相邻的多段线")
        for i, group in enumerate(connected_groups):
            print(f"  第 {i + 1} 组: {len(group)} 个多段线")

        # 合并多段线
        print("\n" + "=" * 80)
        print("开始合并多段线...")
        print("=" * 80)
        merged_count = 0
        total_deleted = 0

        for i, group in enumerate(connected_groups):
            print(f"\n处理第 {i + 1} 组...")

            if len(group) == 1:
                print(f"  该组只有1个多段线，跳过")
                continue

            # 显示组内多段线详情
            print(f"  组内多段线详情:")
            for j, poly in enumerate(group):
                props = get_polyline_properties(poly)
                start, end = get_polyline_endpoints(poly)
                print(f"    多段线 {j+1}: 顶点数={len(props['vertices']) if props else '?'}, "
                      f"起点=({start[0]:.3f},{start[1]:.3f}), 终点=({end[0]:.3f},{end[1]:.3f})")

            # 构建合并路径
            merged_vertices = build_merged_path(group, tolerance)

            if not merged_vertices:
                print(f"  ✗ 无法构建合并路径")
                continue

            print(f"  合并后顶点数: {len(merged_vertices)}")
            print(f"  合并路径:")
            for j, vertex in enumerate(merged_vertices):
                print(f"    点{j+1}: ({vertex[0]:.3f}, {vertex[1]:.3f})")

            # 创建新多段线
            new_polyline = create_merged_polyline(doc, merged_vertices, group)

            if new_polyline:
                # 删除原始多段线
                deleted = delete_original_polylines(group)
                total_deleted += deleted
                merged_count += 1
                print(f"  ✓ 成功合并 {len(group)} 个多段线为1个")
            else:
                print(f"  ✗ 创建新多段线失败")

        # 刷新显示
        doc.Regen(1)  # acActiveViewport

        # 统计结果
        print(f"\n" + "=" * 80)
        print(f"合并完成！")
        print(f"原始多段线数: {len(polylines)}")
        print(f"成功合并的组数: {merged_count}")
        print(f"删除的多段线数: {total_deleted}")
        print(f"新创建的多段线数: {merged_count}")
        print(f"剩余多段线数: {len(polylines) - total_deleted + merged_count}")
        print(f"=" * 80)

        # 删除选择集
        try:
            sset.Delete()
        except:
            pass

        # 保存文件（可选）
        save_choice = input("\n是否保存更改？(y/n): ")
        if save_choice.lower() == 'y':
            try:
                doc.Save()
                print("✓ 文件已保存")
            except Exception as save_error:
                print(f"✗ 保存失败: {str(save_error)}")
                print("请在AutoCAD中手动保存文件")
        else:
            print("✗ 未保存更改")

    except Exception as e:
        print(f"\n✗ 出错: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理COM对象引用
        doc = None
        acad = None


# 主程序
if __name__ == "__main__":
    # 文件路径
    dwg_file_path = r"D:\myWork\2025\0805_桥路处理\data\铁三院0805.dwg"

    # 要处理的图层名称
    layer_name = "450301"

    # 容差值（判断两点是否重合的距离阈值）
    tolerance = 0.1

    # 执行合并操作
    merge_polylines_in_layer(dwg_file_path, layer_name, tolerance)

    input("\n按Enter键退出...")