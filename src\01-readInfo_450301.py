import win32com.client
import time
import os


def print_layer_info(dwg_file_path, layer_name="450301"):
    """读取并打印指定图层的基本信息"""

    print(f"=" * 80)
    print(f"读取图层信息：{layer_name}")
    print(f"=" * 80)
    print(f"DWG文件: {dwg_file_path}")
    print(f"=" * 80)

    # 检查文件是否存在
    if not os.path.exists(dwg_file_path):
        print(f"错误：文件不存在 - {dwg_file_path}")
        return

    acad = None
    doc = None

    try:
        # 连接AutoCAD
        print("\n正在连接AutoCAD...")
        acad = win32com.client.Dispatch("AutoCAD.Application")
        acad.Visible = True
        time.sleep(1)
        print("✓ 连接成功")

        # 打开文件
        print("\n正在打开文件...")
        doc = acad.Documents.Open(dwg_file_path)
        time.sleep(2)
        doc = acad.ActiveDocument
        print(f"✓ 文件打开成功: {doc.Name}")

        # 获取图层集合
        layers = doc.Layers
        print(f"\n文档中共有 {layers.Count} 个图层")

        # 查找目标图层
        target_layer = None
        for i in range(layers.Count):
            layer = layers.Item(i)
            if layer.Name == layer_name:
                target_layer = layer
                break

        if target_layer is None:
            print(f"\n✗ 未找到图层 '{layer_name}'")

            # 列出所有包含'4503'的图层
            print("\n包含'4503'的图层列表：")
            similar_layers = []
            for i in range(layers.Count):
                layer = layers.Item(i)
                if '4503' in layer.Name:
                    similar_layers.append(layer.Name)

            if similar_layers:
                for name in sorted(similar_layers):
                    print(f"  - {name}")
            else:
                print("  未找到包含'4503'的图层")

            return

        # 打印图层信息
        print(f"\n✓ 找到图层 '{layer_name}'")
        print("\n【图层基本信息】")
        print("-" * 40)

        # 基本属性
        print(f"图层名称: {target_layer.Name}")
        print(f"颜色索引: {target_layer.color}")
        print(f"线型: {target_layer.Linetype}")
        print(f"线宽: {target_layer.Lineweight}")
        print(f"打印样式: {target_layer.PlotStyleName}")
        print(f"描述: {target_layer.Description}")

        # 状态属性
        print("\n【图层状态】")
        print("-" * 40)
        print(f"开/关状态: {'开' if target_layer.LayerOn else '关'}")
        print(f"冻结状态: {'冻结' if target_layer.Freeze else '未冻结'}")
        print(f"锁定状态: {'锁定' if target_layer.Lock else '未锁定'}")
        print(f"可打印: {'是' if target_layer.Plottable else '否'}")

        # 在视口中的状态
        try:
            print(f"在新视口中冻结: {'是' if target_layer.ViewportDefault else '否'}")
        except:
            print(f"在新视口中冻结: 无法获取")

        # 统计该图层上的对象数量
        print("\n【图层对象统计】")
        print("-" * 40)

        # 使用选择集统计对象
        try:
            # 创建选择集
            ss_name = f"Count_{layer_name}_{int(time.time() * 1000)}"

            # 删除同名选择集
            try:
                doc.SelectionSets.Item(ss_name).Delete()
            except:
                pass

            sset = doc.SelectionSets.Add(ss_name)

            # 创建过滤器 - 选择指定图层的所有对象
            filter_type = win32com.client.VARIANT(pythoncom.VT_ARRAY | pythoncom.VT_I2, [8])
            filter_data = win32com.client.VARIANT(pythoncom.VT_ARRAY | pythoncom.VT_VARIANT, [layer_name])

            # 选择对象
            sset.Select(5, None, None, filter_type, filter_data)  # 5 = acSelectionSetAll

            total_objects = sset.Count
            print(f"对象总数: {total_objects}")

            if total_objects > 0:
                # 统计对象类型
                object_types = {}
                sample_size = min(1000, total_objects)  # 最多统计1000个对象

                for i in range(sample_size):
                    try:
                        obj = sset.Item(i)
                        obj_type = obj.ObjectName
                        if obj_type in object_types:
                            object_types[obj_type] += 1
                        else:
                            object_types[obj_type] = 1
                    except:
                        continue

                print(f"\n对象类型分布（前{sample_size}个对象）：")
                for obj_type, count in sorted(object_types.items(), key=lambda x: x[1], reverse=True):
                    percentage = count / sample_size * 100
                    print(f"  {obj_type}: {count} ({percentage:.1f}%)")

                # 如果是多段线，检查其属性
                if any('POLYLINE' in t for t in object_types.keys()):
                    print("\n【多段线属性示例】（前5个）")
                    print("-" * 40)
                    polyline_count = 0

                    for i in range(min(100, total_objects)):
                        try:
                            obj = sset.Item(i)
                            if 'POLYLINE' in obj.ObjectName.upper():
                                polyline_count += 1
                                print(f"\n多段线 #{polyline_count}:")
                                print(f"  类型: {obj.ObjectName}")
                                print(f"  闭合: {'是' if obj.Closed else '否'}")

                                # 安全地获取属性
                                try:
                                    print(f"  顶点数: {obj.NumberOfVertices}")
                                except:
                                    print(f"  顶点数: 无法获取")

                                try:
                                    print(f"  全局宽度: {obj.ConstantWidth}")
                                except:
                                    print(f"  全局宽度: 不支持")

                                try:
                                    print(f"  面积: {obj.Area:.2f}")
                                except:
                                    print(f"  面积: 无法计算")

                                try:
                                    print(f"  长度: {obj.Length:.2f}")
                                except:
                                    print(f"  长度: 无法计算")

                                if polyline_count >= 5:
                                    break
                        except:
                            continue

            # 删除选择集
            try:
                sset.Delete()
            except:
                pass

        except Exception as e:
            print(f"统计对象时出错: {str(e)}")

        # 显示图层的线型详细信息
        print("\n【线型信息】")
        print("-" * 40)
        try:
            linetype_name = target_layer.Linetype
            print(f"当前线型: {linetype_name}")

            # 查找线型定义
            linetypes = doc.Linetypes
            for i in range(linetypes.Count):
                lt = linetypes.Item(i)
                if lt.Name == linetype_name:
                    print(f"线型描述: {lt.Description}")
                    break
        except Exception as e:
            print(f"获取线型信息时出错: {str(e)}")

        print("\n" + "=" * 80)
        print("图层信息读取完成！")
        print("=" * 80)

    except Exception as e:
        print(f"\n✗ 出错: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # 关闭文档（可选）
        if doc:
            try:
                # 如果需要关闭文档，取消下面的注释
                # doc.Close(False)  # False = 不保存
                pass
            except:
                pass

        # 清理COM对象引用
        doc = None
        acad = None


# 主程序
if __name__ == "__main__":
    import pythoncom

    # 文件路径
    dwg_file_path = r"D:\myWork\2025\0805_桥路处理\data\铁三院0805.dwg"

    # 要查询的图层名称
    layer_name = "450301"

    # 读取图层信息
    print_layer_info(dwg_file_path, layer_name)

    input("\n按Enter键退出...")